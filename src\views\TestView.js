// SVG microphone icon as string
const microphoneIcon = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
  <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"/>
  <path d="M19 10v2a7 7 0 0 1-14 0v-2a1 1 0 0 1 2 0v2a5 5 0 0 0 10 0v-2a1 1 0 0 1 2 0z"/>
  <path d="M12 19a1 1 0 0 1 1 1v1a1 1 0 0 1-2 0v-1a1 1 0 0 1 1-1z"/>
</svg>`;

// Stop icon for recording state
const stopIcon = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
  <rect x="6" y="6" width="12" height="12" rx="2"/>
</svg>`;

class TestView {
  constructor() {
    this.container = null;
    this.floatingMic = null;
    this.recordingCallback = null;
    this.isRecording = false;
    this.isRecordingAvailable = false;
    this.durationDisplay = null;
    this.statusDisplay = null;
    this.errorDisplay = null;
  }

  render() {
    this.container = document.createElement('div');
    this.container.className = 'container';

    // Test text
    const testText = document.createElement('p');
    testText.textContent = 'I went to the store to buy some groceries. The store was busy, and there was a long line at the checkout. I still managed to get everything I needed before going home.';
    testText.className = 'test-text';

    // Recording status display
    this.statusDisplay = document.createElement('div');
    this.statusDisplay.className = 'recording-status';
    this.statusDisplay.style.display = 'none';

    // Duration display
    this.durationDisplay = document.createElement('div');
    this.durationDisplay.className = 'recording-duration';
    this.durationDisplay.textContent = '0:00';
    this.durationDisplay.style.display = 'none';

    // Error display
    this.errorDisplay = document.createElement('div');
    this.errorDisplay.className = 'error-message';
    this.errorDisplay.style.display = 'none';

    // Floating microphone button
    this.floatingMic = document.createElement('button');
    this.floatingMic.innerHTML = microphoneIcon;
    this.floatingMic.className = 'floating-microphone';
    this.floatingMic.style.viewTransitionName = 'microphone-button';
    this.floatingMic.disabled = true; // Disabled until recording is available

    const svgIcon = this.floatingMic.querySelector('svg');
    if (svgIcon) {
      svgIcon.setAttribute('class', 'microphone-icon');
    }

    // Add click handler
    this.floatingMic.addEventListener('click', () => {
      if (this.recordingCallback && this.isRecordingAvailable) {
        this.recordingCallback();
      }
    });

    // Add elements to container
    this.container.appendChild(testText);
    this.container.appendChild(this.statusDisplay);
    this.container.appendChild(this.durationDisplay);
    this.container.appendChild(this.errorDisplay);

    // Add floating mic to body (outside container for positioning)
    document.body.appendChild(this.floatingMic);

    return this.container;
  }

  /**
   * Set recording callback function
   */
  setRecordingCallback(callback) {
    this.recordingCallback = callback;
  }

  /**
   * Set recording availability status
   */
  setRecordingAvailable(available) {
    this.isRecordingAvailable = available;

    if (this.floatingMic) {
      this.floatingMic.disabled = !available;

      if (available) {
        this.floatingMic.classList.add('available');
        this.floatingMic.title = 'Click to start recording';
      } else {
        this.floatingMic.classList.remove('available');
        this.floatingMic.title = 'Microphone not available';
      }
    }
  }

  /**
   * Set recording state and update UI
   */
  setRecordingState(recording) {
    this.isRecording = recording;

    if (this.floatingMic) {
      if (recording) {
        this.floatingMic.innerHTML = stopIcon;
        this.floatingMic.classList.add('recording');
        this.floatingMic.title = 'Click to stop recording';

        // Update status
        this.showStatus('🔴 Recording...', 'recording');
        this.durationDisplay.style.display = 'block';
      } else {
        this.floatingMic.innerHTML = microphoneIcon;
        this.floatingMic.classList.remove('recording');
        this.floatingMic.title = 'Click to start recording';

        // Hide status and duration
        this.hideStatus();
        this.durationDisplay.style.display = 'none';
      }

      // Update icon class
      const svgIcon = this.floatingMic.querySelector('svg');
      if (svgIcon) {
        svgIcon.setAttribute('class', 'microphone-icon');
      }
    }
  }

  /**
   * Update recording duration display
   */
  updateRecordingDuration(seconds) {
    if (this.durationDisplay) {
      const mins = Math.floor(seconds / 60);
      const secs = Math.floor(seconds % 60);
      this.durationDisplay.textContent = `${mins}:${secs.toString().padStart(2, '0')}`;
    }
  }

  /**
   * Show recording completion message
   */
  showRecordingComplete(recordingId, duration, size) {
    const durationText = `${Math.floor(duration / 60)}:${Math.floor(duration % 60).toString().padStart(2, '0')}`;
    const sizeText = `${(size / 1024).toFixed(1)} KB`;

    this.showStatus(
      `✅ Recording saved! Duration: ${durationText}, Size: ${sizeText}`,
      'success'
    );

    // Auto-hide after 5 seconds
    setTimeout(() => {
      this.hideStatus();
    }, 5000);
  }

  /**
   * Show status message
   */
  showStatus(message, type = 'info') {
    if (this.statusDisplay) {
      this.statusDisplay.textContent = message;
      this.statusDisplay.className = `recording-status ${type}`;
      this.statusDisplay.style.display = 'block';
    }
  }

  /**
   * Hide status message
   */
  hideStatus() {
    if (this.statusDisplay) {
      this.statusDisplay.style.display = 'none';
    }
  }

  /**
   * Show error message
   */
  showError(message) {
    if (this.errorDisplay) {
      this.errorDisplay.textContent = `❌ ${message}`;
      this.errorDisplay.style.display = 'block';

      // Auto-hide after 5 seconds
      setTimeout(() => {
        this.errorDisplay.style.display = 'none';
      }, 5000);
    }
  }

  destroy() {
    if (this.container && this.container.parentNode) {
      this.container.parentNode.removeChild(this.container);
    }

    if (this.floatingMic && this.floatingMic.parentNode) {
      this.floatingMic.parentNode.removeChild(this.floatingMic);
    }
  }
}

export default TestView;
